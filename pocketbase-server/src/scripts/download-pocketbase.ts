#!/usr/bin/env bun

import { $ } from "bun";
import { existsSync } from "fs";
import { join } from "path";

const POCKETBASE_VERSION = "0.23.4";
const PLATFORM = process.platform === "darwin" ? "darwin" : "linux";
const ARCH = process.arch === "arm64" ? "arm64" : "amd64";
const FILENAME = `pocketbase_${POCKETBASE_VERSION}_${PLATFORM}_${ARCH}.zip`;
const DOWNLOAD_URL = `https://github.com/pocketbase/pocketbase/releases/download/v${POCKETBASE_VERSION}/${FILENAME}`;

async function downloadPocketBase() {
  console.log("🚀 Downloading PocketBase...");
  console.log(`Platform: ${PLATFORM}_${ARCH}`);
  console.log(`Version: ${POCKETBASE_VERSION}`);
  
  const workspaceDir = join(import.meta.dir, "../..");
  const pocketbasePath = join(workspaceDir, "pocketbase");
  
  // Check if PocketBase already exists
  if (existsSync(pocketbasePath)) {
    console.log("✅ PocketBase binary already exists");
    return;
  }
  
  try {
    // Download PocketBase
    console.log(`📥 Downloading from: ${DOWNLOAD_URL}`);
    await $`curl -L -o ${FILENAME} ${DOWNLOAD_URL}`.cwd(workspaceDir);
    
    // Extract the binary
    console.log("📦 Extracting PocketBase...");
    await $`unzip -o ${FILENAME}`.cwd(workspaceDir);
    
    // Make it executable
    await $`chmod +x pocketbase`.cwd(workspaceDir);
    
    // Clean up zip file
    await $`rm ${FILENAME}`.cwd(workspaceDir);
    
    console.log("✅ PocketBase downloaded and ready!");
    console.log(`📍 Location: ${pocketbasePath}`);
    
  } catch (error) {
    console.error("❌ Failed to download PocketBase:", error);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (import.meta.main) {
  await downloadPocketBase();
}
