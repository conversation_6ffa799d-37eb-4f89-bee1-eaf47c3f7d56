{"name": "urusan-financial", "version": "0.4.0", "description": "A monorepo template built with Bun, Hono, Vite, and React", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/steved<PERSON>ev/bhvr", "packageManager": "bun@1.2.4", "workspaces": ["./server", "./client", "./shared", "./pocketbase-server"], "scripts": {"dev": "turbo dev", "dev:client": "turbo dev --filter=client", "dev:server": "turbo dev --filter=server", "build": "turbo build", "build:client": "turbo build --filter=client", "build:server": "turbo build --filter=server", "lint": "turbo lint", "type-check": "turbo type-check", "test": "turbo test", "postinstall": "turbo build --filter=shared --filter=server"}, "keywords": ["bun", "hono", "react", "vite", "monorepo", "turbo"], "devDependencies": {"bun-types": "latest", "turbo": "^2.5.5"}, "peerDependencies": {"typescript": "^5.7.3"}, "dependencies": {"hono": "^4.8.9"}}