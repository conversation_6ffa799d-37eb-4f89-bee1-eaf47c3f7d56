#!/usr/bin/env bun

import { $ } from "bun";
import { existsSync } from "fs";
import { join } from "path";

const workspaceDir = join(import.meta.dir, "..");
const pocketbasePath = join(workspaceDir, "pocketbase");

async function startProduction() {
  console.log("🚀 Starting PocketBase in production mode...");
  
  // Check if PocketBase binary exists
  if (!existsSync(pocketbasePath)) {
    console.error("❌ PocketBase binary not found. Run 'bun run pb:download' first.");
    process.exit(1);
  }
  
  // Set production environment variables
  const env = {
    ...process.env,
    PB_PORT: process.env.PB_PORT || "8090",
    PB_HOST: process.env.PB_HOST || "0.0.0.0",
    PB_DATA_DIR: process.env.PB_DATA_DIR || "./pb_data",
  };
  
  console.log(`🌐 Starting server on http://${env.PB_HOST}:${env.PB_PORT}`);
  console.log(`📊 Admin UI: http://${env.PB_HOST}:${env.PB_PORT}/_/`);
  console.log(`🔧 Data directory: ${env.PB_DATA_DIR}`);
  
  try {
    // Start PocketBase in production mode
    await $`./pocketbase serve --http=${env.PB_HOST}:${env.PB_PORT} --dir=${env.PB_DATA_DIR}`.env(env).cwd(workspaceDir);
  } catch (error) {
    console.error("❌ Failed to start PocketBase:", error);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (import.meta.main) {
  await startProduction();
}
