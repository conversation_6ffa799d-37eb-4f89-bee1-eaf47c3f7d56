import PocketBase from 'pocketbase';
import { getPocketBaseUrl } from './index';

/**
 * Create a PocketBase client instance
 */
export function createPocketBaseClient(url?: string): PocketBase {
  const baseUrl = url || getPocketBaseUrl();
  return new PocketBase(baseUrl);
}

/**
 * Default PocketBase client instance
 */
export const pb = createPocketBaseClient();

/**
 * PocketBase client with authentication helpers
 */
export class PocketBaseClient {
  private client: PocketBase;

  constructor(url?: string) {
    this.client = createPocketBaseClient(url);
  }

  get instance(): PocketBase {
    return this.client;
  }

  /**
   * Authenticate with email and password
   */
  async authWithPassword(email: string, password: string) {
    try {
      const authData = await this.client.collection('users').authWithPassword(email, password);
      return authData;
    } catch (error) {
      console.error('Authentication failed:', error);
      throw error;
    }
  }

  /**
   * Register a new user
   */
  async register(email: string, password: string, passwordConfirm: string, additionalData?: Record<string, any>) {
    try {
      const userData = {
        email,
        password,
        passwordConfirm,
        ...additionalData,
      };
      
      const user = await this.client.collection('users').create(userData);
      return user;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  }

  /**
   * Logout current user
   */
  logout() {
    this.client.authStore.clear();
  }

  /**
   * Check if user is authenticated
   */
  get isAuthenticated(): boolean {
    return this.client.authStore.isValid;
  }

  /**
   * Get current user
   */
  get currentUser() {
    return this.client.authStore.model;
  }

  /**
   * Get auth token
   */
  get token(): string {
    return this.client.authStore.token;
  }
}
