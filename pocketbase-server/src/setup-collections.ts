#!/usr/bin/env bun

import PocketBase from 'pocketbase';

const pb = new PocketBase('http://127.0.0.1:8090');

// Collection schemas for the financial app
const collections = [
  {
    name: 'financial_buckets',
    type: 'base',
    schema: [
      { name: 'name', type: 'text', required: true },
      { name: 'description', type: 'text' },
      { name: 'emoji', type: 'text', required: true },
      { name: 'owner', type: 'relation', required: true, options: { collectionId: 'users', cascadeDelete: false } },
      { name: 'collaborators', type: 'relation', options: { collectionId: 'users', cascadeDelete: false, maxSelect: null } },
      { name: 'currency', type: 'text', required: true, options: { pattern: '^[A-Z]{3}$' } },
      { name: 'balance', type: 'number', required: true, options: { min: 0 } }
    ]
  },
  {
    name: 'transaction_categories',
    type: 'base',
    schema: [
      { name: 'name', type: 'text', required: true },
      { name: 'color', type: 'text', required: true },
      { name: 'icon', type: 'text' },
      { name: 'bucket', type: 'relation', required: true, options: { collectionId: 'financial_buckets', cascadeDelete: true } }
    ]
  },
  {
    name: 'transactions',
    type: 'base',
    schema: [
      { name: 'amount', type: 'number', required: true },
      { name: 'description', type: 'text', required: true },
      { name: 'category', type: 'relation', required: true, options: { collectionId: 'transaction_categories', cascadeDelete: false } },
      { name: 'bucket', type: 'relation', required: true, options: { collectionId: 'financial_buckets', cascadeDelete: true } },
      { name: 'user', type: 'relation', required: true, options: { collectionId: 'users', cascadeDelete: false } },
      { name: 'date', type: 'date', required: true },
      { name: 'type', type: 'select', required: true, options: { values: ['income', 'expense', 'transfer'] } },
      { name: 'tags', type: 'text' },
      { name: 'receipt', type: 'file', options: { maxSelect: 1, maxSize: 5242880 } }
    ]
  },
  {
    name: 'budgets',
    type: 'base',
    schema: [
      { name: 'name', type: 'text', required: true },
      { name: 'amount', type: 'number', required: true, options: { min: 0 } },
      { name: 'period', type: 'select', required: true, options: { values: ['weekly', 'monthly', 'yearly'] } },
      { name: 'category', type: 'relation', required: true, options: { collectionId: 'transaction_categories', cascadeDelete: false } },
      { name: 'bucket', type: 'relation', required: true, options: { collectionId: 'financial_buckets', cascadeDelete: true } },
      { name: 'start_date', type: 'date', required: true },
      { name: 'end_date', type: 'date', required: true }
    ]
  },
  {
    name: 'goals',
    type: 'base',
    schema: [
      { name: 'name', type: 'text', required: true },
      { name: 'description', type: 'text' },
      { name: 'target_amount', type: 'number', required: true, options: { min: 0 } },
      { name: 'current_amount', type: 'number', required: true, options: { min: 0 } },
      { name: 'target_date', type: 'date', required: true },
      { name: 'bucket', type: 'relation', required: true, options: { collectionId: 'financial_buckets', cascadeDelete: true } },
      { name: 'user', type: 'relation', required: true, options: { collectionId: 'users', cascadeDelete: false } },
      { name: 'status', type: 'select', required: true, options: { values: ['active', 'completed', 'paused'] } }
    ]
  }
];

async function setupCollections() {
  console.log('🚀 Setting up PocketBase collections...');
  
  try {
    // Note: You'll need to authenticate as admin first
    console.log('⚠️  Please make sure you have created an admin user first');
    console.log('   Run: bun run admin');
    console.log('   Or visit: http://127.0.0.1:8090/_/');
    
    for (const collection of collections) {
      try {
        console.log(`📦 Creating collection: ${collection.name}`);
        await pb.collections.create(collection);
        console.log(`✅ Created collection: ${collection.name}`);
      } catch (error: any) {
        if (error.status === 400 && error.data?.name?.message?.includes('already exists')) {
          console.log(`⚠️  Collection ${collection.name} already exists, skipping...`);
        } else {
          console.error(`❌ Failed to create collection ${collection.name}:`, error);
        }
      }
    }
    
    console.log('✅ Collection setup completed!');
    console.log('🌐 Admin UI: http://127.0.0.1:8090/_/');
    console.log('📊 API Base: http://127.0.0.1:8090/api/');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (import.meta.main) {
  await setupCollections();
}
