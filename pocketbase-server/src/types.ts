/**
 * PocketBase Types for Urusan Financial
 */

// Base record interface that all PocketBase records extend
export interface BaseRecord {
  id: string;
  created: string;
  updated: string;
}

// User record interface
export interface User extends BaseRecord {
  email: string;
  name: string;
  avatar?: string;
  verified: boolean;
}

// Financial bucket/account interface
export interface FinancialBucket extends BaseRecord {
  name: string;
  description?: string;
  emoji: string;
  owner: string; // User ID
  collaborators: string[]; // Array of User IDs
  currency: string;
  balance: number;
}

// Transaction category interface
export interface TransactionCategory extends BaseRecord {
  name: string;
  color: string;
  icon?: string;
  bucket: string; // FinancialBucket ID
}

// Transaction interface
export interface Transaction extends BaseRecord {
  amount: number;
  description: string;
  category: string; // TransactionCategory ID
  bucket: string; // FinancialBucket ID
  user: string; // User ID
  date: string;
  type: 'income' | 'expense' | 'transfer';
  tags?: string[];
  receipt?: string; // File ID for receipt image
}

// Budget interface
export interface Budget extends BaseRecord {
  name: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  category: string; // TransactionCategory ID
  bucket: string; // FinancialBucket ID
  startDate: string;
  endDate: string;
}

// Goal interface
export interface Goal extends BaseRecord {
  name: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  bucket: string; // FinancialBucket ID
  user: string; // User ID
  status: 'active' | 'completed' | 'paused';
}

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  FINANCIAL_BUCKETS: 'financial_buckets',
  TRANSACTION_CATEGORIES: 'transaction_categories',
  TRANSACTIONS: 'transactions',
  BUDGETS: 'budgets',
  GOALS: 'goals',
} as const;

// PocketBase collection type mapping
export interface Collections {
  [COLLECTIONS.USERS]: User;
  [COLLECTIONS.FINANCIAL_BUCKETS]: FinancialBucket;
  [COLLECTIONS.TRANSACTION_CATEGORIES]: TransactionCategory;
  [COLLECTIONS.TRANSACTIONS]: Transaction;
  [COLLECTIONS.BUDGETS]: Budget;
  [COLLECTIONS.GOALS]: Goal;
}
