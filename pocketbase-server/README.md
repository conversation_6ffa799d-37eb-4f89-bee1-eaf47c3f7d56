# PocketBase Server

This is the PocketBase server workspace for the urusan-financial application. PocketBase provides a real-time database with built-in authentication, file storage, and admin dashboard.

## Features

- Real-time database with SQLite backend
- Built-in authentication and user management
- File storage and uploads
- Admin dashboard UI
- REST and realtime APIs
- TypeScript SDK integration

## Setup

1. Install dependencies:
```bash
bun install
```

2. Download PocketBase binary and setup admin:
```bash
bun run setup
```

3. Start development server:
```bash
bun run dev
```

## Scripts

- `bun run dev` - Start PocketBase in development mode with auto-reload
- `bun run start` - Start PocketBase in production mode
- `bun run pb:download` - Download the latest PocketBase binary
- `bun run pb:serve` - Start PocketBase server directly
- `bun run pb:admin` - Create admin user
- `bun run pb:migrate` - Run database migrations
- `bun run pb:seed` - Seed database with initial data

## Configuration

PocketBase configuration is stored in `pb_data/` directory:
- `pb_data/data.db` - SQLite database
- `pb_data/logs.db` - Application logs
- `pb_data/storage/` - File uploads

## API Endpoints

- Admin UI: http://localhost:8090/_/
- API Base: http://localhost:8090/api/
- Collections: http://localhost:8090/api/collections/
- Auth: http://localhost:8090/api/collections/users/auth-with-password

## Environment Variables

- `PB_PORT` - Server port (default: 8090)
- `PB_HOST` - Server host (default: 127.0.0.1)
- `PB_DATA_DIR` - Data directory (default: ./pb_data)
