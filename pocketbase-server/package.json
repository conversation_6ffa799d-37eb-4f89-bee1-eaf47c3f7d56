{"name": "pocketbase-server", "version": "0.0.1", "description": "PocketBase server for urusan-financial", "main": "src/index.ts", "scripts": {"dev": "./pocketbase serve --dev --http=127.0.0.1:8090", "start": "./pocketbase serve --http=0.0.0.0:8090", "admin": "./pocketbase admin create", "migrate": "./pocketbase migrate", "setup-collections": "bun run src/setup-collections.ts", "build": "echo 'PocketBase is a binary, no build needed'"}, "dependencies": {"pocketbase": "^0.21.5", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest", "typescript": "^5.7.3"}, "keywords": ["pocketbase", "backend", "database", "api"]}