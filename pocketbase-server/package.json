{"name": "pocketbase-server", "version": "0.0.1", "description": "PocketBase server for urusan-financial", "main": "src/index.ts", "scripts": {"dev": "bun run src/dev.ts", "start": "bun run src/start.ts", "build": "bun build src/index.ts --outdir dist --target bun", "pb:download": "bun run src/scripts/download-pocketbase.ts", "pb:serve": "./pocketbase serve --http=127.0.0.1:8090", "pb:admin": "./pocketbase admin create", "pb:migrate": "bun run src/scripts/migrate.ts", "pb:seed": "bun run src/scripts/seed.ts", "setup": "bun run pb:download && bun run pb:admin"}, "dependencies": {"pocketbase": "^0.21.5", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest", "typescript": "^5.7.3"}, "keywords": ["pocketbase", "backend", "database", "api"]}