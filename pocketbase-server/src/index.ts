/**
 * PocketBase Server for Urusan Financial
 * 
 * This module provides utilities and configurations for the PocketBase server.
 * PocketBase is used as a real-time database with built-in authentication,
 * file storage, and admin dashboard.
 */

export * from './client';
export * from './types';

// Re-export PocketBase SDK
export { default as PocketBase } from 'pocketbase';

// Configuration constants
export const POCKETBASE_CONFIG = {
  DEFAULT_PORT: 8090,
  DEFAULT_HOST: '127.0.0.1',
  DEFAULT_DATA_DIR: './pb_data',
  API_BASE_PATH: '/api/',
  ADMIN_UI_PATH: '/_/',
} as const;

// Environment helpers
export function getPocketBaseUrl(host?: string, port?: number): string {
  const pbHost = host || process.env.PB_HOST || POCKETBASE_CONFIG.DEFAULT_HOST;
  const pbPort = port || parseInt(process.env.PB_PORT || '') || POCKETBASE_CONFIG.DEFAULT_PORT;
  return `http://${pbHost}:${pbPort}`;
}

export function getAdminUrl(host?: string, port?: number): string {
  return `${getPocketBaseUrl(host, port)}${POCKETBASE_CONFIG.ADMIN_UI_PATH}`;
}

export function getApiUrl(host?: string, port?: number): string {
  return `${getPocketBaseUrl(host, port)}${POCKETBASE_CONFIG.API_BASE_PATH}`;
}
